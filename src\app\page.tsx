
"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Award, CheckCircle, Search, User, ShieldCheck, UploadCloud, QrCode } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useWallet } from "@/contexts/WalletContext";
import { useState, useEffect } from "react";

export default function HomePage() {
  const { isAdmin } = useWallet();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const features = [
    {
      icon: <UploadCloud className="h-10 w-10 text-primary" />,
      title: "Issue Certificates",
      description: "Admins can easily mint and issue digital certificates as NFTs or hash-based entries.",
      link: "/admin",
      linkText: "Admin Dashboard"
    },
    {
      icon: <User className="h-10 w-10 text-primary" />,
      title: "Student Access",
      description: "Students can view all their academic achievements securely stored on the blockchain.",
      link: "/student",
      linkText: "My Certificates"
    },
    {
      icon: <ShieldCheck className="h-10 w-10 text-primary" />,
      title: "Verify Authenticity",
      description: "Third parties can instantly verify the authenticity of any certificate with a hash or wallet address.",
      link: "/verify",
      linkText: "Verify Certificate"
    },
    {
      icon: <QrCode className="h-10 w-10 text-primary" />,
      title: "QR Code Enabled",
      description: "Each certificate comes with a unique QR code for quick access and verification.",
      link: "/verify",
      linkText: "Learn More"
    },
  ];

  return (
    <div className="space-y-12">
      <section className="text-center py-12 md:py-20 bg-gradient-to-br from-primary/10 via-background to-accent/10 rounded-lg shadow-sm">
        <Award className="mx-auto h-20 w-20 text-primary mb-6" />
        <h1 className="text-4xl md:text-5xl font-bold font-headline mb-4 text-foreground">
          Welcome to CertiProof
        </h1>
        <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
          The future of academic certification. Secure, transparent, and verifiable university certificates on the blockchain.
        </p>
        <div className="space-y-2 sm:space-y-0 sm:space-x-4 flex flex-col sm:flex-row justify-center items-center">
          <Button size="lg" asChild className="bg-primary hover:bg-primary/90 text-primary-foreground w-full sm:w-auto">
            <Link href="/verify">Verify a Certificate</Link>
          </Button>
          <Button size="lg" variant="outline" asChild className="w-full sm:w-auto">
            <Link href="/admin">{mounted && isAdmin ? "Admin Dashboard" : "Admin Login"}</Link>
          </Button>
        </div>
      </section>

      <section className="py-12">
        <h2 className="text-3xl font-bold text-center mb-10 font-headline text-foreground">Platform Features</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col">
              <CardHeader className="items-center text-center">
                {feature.icon}
                <CardTitle className="mt-4 font-headline">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent className="text-center flex-grow">
                <p className="text-muted-foreground mb-4">{feature.description}</p>
              </CardContent>
              <div className="p-6 pt-0 text-center">
                <Button variant="link" asChild className="text-primary">
                  <Link href={feature.link}>{feature.linkText} <CheckCircle className="ml-2 h-4 w-4" /></Link>
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </section>

      <section className="py-12 bg-card rounded-lg shadow-sm">
        <div className="container mx-auto px-6 text-center md:text-left">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-4 font-headline text-foreground">Why CertiProof?</h2>
              <ul className="space-y-3 text-muted-foreground mb-6">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-accent mr-2 mt-1 shrink-0" />
                  <span><strong>Tamper-Proof:</strong> Certificates stored on the blockchain are immutable and secure.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-accent mr-2 mt-1 shrink-0" />
                  <span><strong>Instant Verification:</strong> Anyone can verify certificates globally, 24/7.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-accent mr-2 mt-1 shrink-0" />
                  <span><strong>Reduced Fraud:</strong> Eliminates the possibility of counterfeit academic credentials.</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-accent mr-2 mt-1 shrink-0" />
                  <span><strong>Student Empowerment:</strong> Students have full ownership and control over their digital certificates.</span>
                </li>
              </ul>
              <Button size="lg" asChild className="bg-accent hover:bg-accent/90 text-accent-foreground">
                <Link href="/#learn-more">Discover More</Link>
              </Button>
            </div>
            <div className="flex justify-center">
              <Image 
                src="https://placehold.co/500x400.png" 
                alt="Blockchain Security Illustration" 
                width={500} 
                height={400} 
                className="rounded-lg shadow-xl"
                data-ai-hint="blockchain security"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

