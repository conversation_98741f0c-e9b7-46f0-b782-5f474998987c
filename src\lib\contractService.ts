
import type { Certificate } from '@/types';
import { ethers, Signer } from 'ethers'; 
import { CERTIFICATE_CONTRACT_ABI, CERTIFICATE_CONTRACT_ADDRESS, ISSUER_NAME, QR_CODE_API_URL, ADMIN_ADDRESSES } from './constants';
import { addCertificateToMockData, getMockCertificates } from './mockData'; 

// Helper to generate QR code URL
const generateQrCodeUrl = (id: string) => {
  const verificationUrl = typeof window !== 'undefined' ? `${window.location.origin}/verify?id=${id}` : `/verify?id=${id}`;
  return `${QR_CODE_API_URL}?size=200x200&data=${encodeURIComponent(verificationUrl)}`;
};


// This function would normally interact with a real contract instance
const getContractInstance = (signerOrProvider: Signer | ethers.Provider) => {
  // In a real app:
  // return new ethers.Contract(CERTIFICATE_CONTRACT_ADDRESS, CERTIFICATE_CONTRACT_ABI, signer<PERSON><PERSON><PERSON><PERSON><PERSON>);
  
  // For mock, return an object with methods that simulate contract calls
  return {
    issueCertificate: async (studentAddress: string, metadataIpfsUrl: string, studentName: string, certificateTitle: string, documentIpfsHash: string): Promise<{ transactionHash: string, certificateId: string }> => {
      console.log(`Simulating contract call: issueCertificate for ${studentAddress} with metadata ${metadataIpfsUrl} and document ${documentIpfsHash}`);
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate transaction time
      
      // Generate more unique IDs for mock data
      const randomSuffix = Math.random().toString(36).substring(2, 10);
      const transactionHash = `0xsim_tx_${Date.now()}_${randomSuffix}`;
      const certificateId = `cert_id_${Date.now()}_${randomSuffix}`;
      
      const newCert: Certificate = {
        id: certificateId,
        title: certificateTitle,
        studentName: studentName,
        studentAddress: studentAddress,
        issueDate: new Date().toISOString(),
        issuerName: ISSUER_NAME,
        certificateIpfsHash: documentIpfsHash, // Use the passed documentIpfsHash
        metadataIpfsUrl: metadataIpfsUrl,
        qrCodeUrl: generateQrCodeUrl(certificateId), // QR code generated here
      };

      // Use the new function to add and save to localStorage via mockData.ts
      addCertificateToMockData(newCert);
      console.log("New certificate added and attempted to save to localStorage:", newCert);

      return { transactionHash, certificateId };
    },
    getCertificatesByOwner: async (ownerAddress: string): Promise<Certificate[]> => {
      console.log(`Simulating contract call: getCertificatesByOwner for ${ownerAddress}`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      const currentCerts = getMockCertificates(); // Ensure we're reading the potentially localStorage-updated list
      console.log('All available certificates:', currentCerts);
      const lowerOwnerAddress = ownerAddress.toLowerCase();
      console.log('Searching for certificates with studentAddress matching:', lowerOwnerAddress);
      const matchingCerts = currentCerts.filter(cert => {
        const certAddress = cert.studentAddress.toLowerCase();
        console.log(`Comparing cert ${cert.id}: ${certAddress} === ${lowerOwnerAddress} ? ${certAddress === lowerOwnerAddress}`);
        return certAddress === lowerOwnerAddress;
      });
      console.log('Matching certificates found:', matchingCerts);
      return matchingCerts;
    },
    getCertificateById: async (certificateId: string): Promise<Certificate | null> => {
      console.log(`Simulating contract call: getCertificateById for ${certificateId}`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      const currentCerts = getMockCertificates(); // Ensure reading latest
      return currentCerts.find(cert => cert.id === certificateId) || null;
    },
    isAdmin: async (address: string): Promise<boolean> => {
      console.log(`Simulating contract isAdmin check for ${address}`);
      await new Promise(resolve => setTimeout(resolve, 500));
      return ADMIN_ADDRESSES.includes(address.toLowerCase());
    },
    getAllCertificates: async (): Promise<Certificate[]> => {
      console.log('Simulating contract call: getAllCertificates');
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getMockCertificates();
    }
  };
};

export const issueCertificate = async (
  signer: Signer, 
  studentAddress: string,
  studentName: string,
  certificateTitle: string,
  metadataIpfsUrl: string,
  documentIpfsHash: string // Added documentIpfsHash
): Promise<{ transactionHash: string, certificateId: string }> => {
  if (!signer) throw new Error("Wallet not connected or signer not available.");
  
  const contract = getContractInstance(signer);
  return contract.issueCertificate(studentAddress, metadataIpfsUrl, studentName, certificateTitle, documentIpfsHash);
};

export const getCertificatesByOwner = async (
  provider: ethers.Provider, 
  ownerAddress: string
): Promise<Certificate[]> => {
  if (!provider) {
    // Allow fetching without provider for public view if contract interaction is truly public or mocked
    console.warn("Provider not available for getCertificatesByOwner. Using mock data directly.");
     const contract = getContractInstance(provider!); // Will use the mock instance, provider can be null here
     return contract.getCertificatesByOwner(ownerAddress);
  }
  const contract = getContractInstance(provider);
  return contract.getCertificatesByOwner(ownerAddress);
};

export const getCertificateById = async (
  provider: ethers.Provider, 
  certificateId: string
): Promise<Certificate | null> => {
   if (!provider) {
    console.warn("Provider not available for getCertificateById. Using mock data directly.");
    const contract = getContractInstance(provider!); // provider can be null here
    return contract.getCertificateById(certificateId);
  }
  const contract = getContractInstance(provider);
  return contract.getCertificateById(certificateId);
};

export const checkAdminRole = async (
  provider: ethers.Provider,
  address: string
): Promise<boolean> => {
  if (!provider) throw new Error("Provider not available.");
  const contract = getContractInstance(provider);
  return contract.isAdmin(address);
};

export const getAllCertificates = async (
  provider: ethers.Provider
): Promise<Certificate[]> => {
  if (!provider) {
    console.warn("Provider not available for getAllCertificates. Using mock data directly.");
    const contract = getContractInstance(provider!);
    return contract.getAllCertificates();
  }
  const contract = getContractInstance(provider);
  return contract.getAllCertificates();
};
