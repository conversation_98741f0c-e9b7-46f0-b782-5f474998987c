"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useWallet } from '@/contexts/WalletContext';
import { useToast } from '@/hooks/use-toast';
import { getCertificatesByOwner } from '@/lib/contractService';
import type { Certificate } from '@/types';
import CertificateCard from '@/components/CertificateCard';
import { Loader2, User, Search, FileText } from 'lucide-react';

export default function StudentPage() {
  const { account, provider, isLoading: walletLoading } = useWallet();
  const { toast } = useToast();
  const [mounted, setMounted] = useState(false);
  const [searchAddress, setSearchAddress] = useState('');
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searched, setSearched] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && account) {
      setSearchAddress(account);
      // Ensure there's a test certificate for this address
      ensureTestCertificateForAddress(account);
      // Auto-fetch certificates when wallet is connected
      handleFetchCertificates(account);
    }
  }, [account, mounted]);

  const ensureTestCertificateForAddress = async (address: string) => {
    try {
      const { getMockCertificates, addCertificateToMockData } = await import('@/lib/mockData');
      const allCerts = getMockCertificates();

      // Check if there's already a certificate for this address
      const existingCert = allCerts.find(cert =>
        cert.studentAddress.toLowerCase() === address.toLowerCase()
      );

      if (!existingCert) {
        // Create a test certificate for this address
        const testCert = {
          id: `test_${address.toLowerCase()}_${Date.now()}`,
          title: 'Test Certificate - Blockchain Basics',
          studentName: 'Test Student',
          studentAddress: address,
          issueDate: new Date().toISOString(),
          issuerName: 'University of Innovation',
          certificateIpfsHash: 'QmYourTestCertificateHash',
          metadataIpfsUrl: `ipfs://QmTestMetadata${address.slice(-6)}/metadata.json`,
          qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(`/verify?id=test_${address.toLowerCase()}_${Date.now()}`)}`
        };

        addCertificateToMockData(testCert);
        console.log('Added test certificate for address:', address);
      }
    } catch (error) {
      console.error('Error ensuring test certificate:', error);
    }
  };

  const handleFetchCertificates = async (addressToSearch: string) => {
    if (!addressToSearch) {
      toast({ title: 'Missing Address', description: 'Please enter a wallet address.', variant: 'destructive' });
      return;
    }
    if (!provider && !walletLoading) { // Check provider only if wallet is not in loading state
        toast({ title: 'Wallet Not Connected', description: 'Please connect your wallet or ensure the provider is available.', variant: 'destructive' });
        return;
    }


    setIsLoading(true);
    setSearched(true);
    setCertificates([]); // Clear previous results

    try {
      console.log('Searching for certificates for address:', addressToSearch);
      // If provider is not available (e.g. wallet not connected but address manually entered),
      // we might need a public provider. For mock, we allow it.
      const fetchedCertificates = await getCertificatesByOwner(provider!, addressToSearch); // provider can be null if not connected
      console.log('Found certificates:', fetchedCertificates);
      setCertificates(fetchedCertificates);
      if (fetchedCertificates.length === 0) {
        toast({
          title: 'No Certificates Found',
          description: `No certificates were found for address: ${addressToSearch.substring(0, 6)}...${addressToSearch.substring(addressToSearch.length - 4)}`
        });
      } else {
        toast({ title: 'Certificates Loaded', description: `Found ${fetchedCertificates.length} certificate(s).` });
      }
    } catch (error: any) {
      console.error('Error fetching certificates:', error);
      toast({ title: 'Error Fetching Certificates', description: error.message || "Could not fetch certificates.", variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleFetchCertificates(searchAddress);
  };

  const handleClearStorage = () => {
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.removeItem('certiproof_certificates');
      toast({ title: 'Storage Cleared', description: 'Local certificate storage has been cleared. Refresh the page to see default certificates.' });
    }
  };

  const handleDebugCertificates = async () => {
    try {
      // Import the function to get all certificates
      const { getMockCertificates } = await import('@/lib/mockData');
      const allCerts = getMockCertificates();
      console.log('=== DEBUG: All Available Certificates ===');
      allCerts.forEach((cert, index) => {
        console.log(`${index + 1}. ${cert.title}`);
        console.log(`   Student: ${cert.studentName}`);
        console.log(`   Address: ${cert.studentAddress}`);
        console.log(`   ID: ${cert.id}`);
        console.log('---');
      });
      console.log(`=== Current Search Address: ${searchAddress} ===`);
      console.log(`=== Connected Wallet: ${account || 'Not connected'} ===`);

      toast({
        title: 'Debug Info Logged',
        description: `Found ${allCerts.length} total certificates. Check console for details.`
      });
    } catch (error) {
      console.error('Debug error:', error);
    }
  };

  return (
    <div className="space-y-8">
      <section>
        <h1 className="text-3xl font-bold mb-2 font-headline">My Certificates</h1>
        <p className="text-muted-foreground">View academic certificates associated with your wallet address.</p>
      </section>

      <Card className="shadow-xl">
        <CardHeader>
          <CardTitle className="font-headline">Search Certificates</CardTitle>
          <CardDescription>Enter a wallet address to find certificates. If your wallet is connected, your address will be pre-filled.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="studentAddress">Student Wallet Address</Label>
              <div className="flex space-x-2">
                <Input
                  id="studentAddress"
                  type="text"
                  placeholder="0x..."
                  value={searchAddress}
                  onChange={(e) => setSearchAddress(e.target.value)}
                  className="flex-grow"
                />
                <Button type="submit" disabled={isLoading || walletLoading} className="bg-primary hover:bg-primary/90 text-primary-foreground">
                  {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Search className="mr-2 h-4 w-4" />}
                  Search
                </Button>
              </div>
            </div>
            { walletLoading && <p className="text-sm text-muted-foreground flex items-center"><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Checking wallet connection...</p>}
            <div className="pt-4 border-t space-y-2">
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleClearStorage}
                  className="text-xs"
                >
                  Clear Test Data
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleDebugCertificates}
                  className="text-xs"
                >
                  Debug Certificates
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                For testing: clear storage to reset, or debug to see all available certificates in console
              </p>
            </div>
          </form>
        </CardContent>
      </Card>

      {isLoading && (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="ml-4 text-lg">Loading Certificates...</p>
        </div>
      )}

      {!isLoading && searched && certificates.length === 0 && (
        <Card className="text-center py-10">
          <CardContent>
            <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <p className="text-xl font-semibold">No Certificates Found</p>
            <p className="text-muted-foreground">There are no certificates registered for the address: <br/> <span className="font-mono text-sm">{searchAddress}</span></p>
          </CardContent>
        </Card>
      )}

      {!isLoading && certificates.length > 0 && (
        <section className="space-y-6">
            <h2 className="text-2xl font-bold font-headline">Found {certificates.length} Certificate(s) for:</h2>
            <p className="text-muted-foreground font-mono break-all">{searchAddress}</p>
          <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-6">
            {certificates.map((cert) => (
              <CertificateCard key={cert.id} certificate={cert} />
            ))}
          </div>
        </section>
      )}
    </div>
  );
}