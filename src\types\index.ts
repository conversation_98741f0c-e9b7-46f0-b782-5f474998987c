
export interface Certificate {
  id: string; // Unique identifier (e.g., transaction hash of minting, or a unique ID from contract event)
  title: string; // e.g., "Bachelor of Science in Computer Science"
  studentName: string;
  studentAddress: string; // Wallet address of the student
  issueDate: string; // ISO string format date
  issuerName: string; // e.g., "University of Innovation"
  certificateIpfsHash: string; // IPFS hash of the actual certificate document (PDF, image)
  metadataIpfsUrl?: string; // Optional: IPFS URL of the metadata JSON (if following NFT standards)
  qrCodeUrl?: string; // URL for the QR code image linking to verification page
}

export interface WalletState {
  account: string | null;
  provider: any | null; // Ideally, this would be ethers.providers.Web3Provider or similar
  signer: any | null; // Ideally, ethers.Signer
  chainId: number | null;
  isAdmin: boolean; // Simulated admin status
  isLoading: boolean;
  error: string | null;
}

export interface WalletContextType extends WalletState {
  connectWallet: () => Promise<void>;
  disconnectWallet: () => void;
  checkAdminStatus: (address: string) => void;
}
