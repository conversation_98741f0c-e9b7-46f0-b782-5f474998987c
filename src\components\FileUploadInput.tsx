"use client";

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UploadCloud, FileText, CheckCircle, AlertCircle } from 'lucide-react';
import React, { useState, ChangeEvent } from 'react';

interface FileUploadInputProps {
  onFileSelect: (file: File | null) => void;
  accept?: string; // e.g., ".pdf,.jpg,.png"
  id?: string;
  label?: string;
}

export default function FileUploadInput({ 
  onFileSelect, 
  accept = ".pdf,.png,.jpg,.jpeg", 
  id = "certificate-file",
  label = "Certificate Document" 
}: FileUploadInputProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    setError(null);
    const file = event.target.files ? event.target.files[0] : null;
    if (file) {
      // Basic validation (example: file size < 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError("File size exceeds 5MB limit.");
        setSelectedFile(null);
        setFileName('');
        onFileSelect(null);
        return;
      }
      // You can add more validation for file types if needed, beyond 'accept' attribute
      setSelectedFile(file);
      setFileName(file.name);
      onFileSelect(file);
    } else {
      setSelectedFile(null);
      setFileName('');
      onFileSelect(null);
    }
  };

  return (
    <div className="space-y-2 w-full">
      {label && <Label htmlFor={id} className="text-sm font-medium">{label}</Label>}
      <div className="flex items-center justify-center w-full">
        <label
          htmlFor={id}
          className="flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg cursor-pointer bg-card hover:bg-muted/50 transition-colors"
        >
          <div className="flex flex-col items-center justify-center pt-5 pb-6">
            {selectedFile ? (
              <>
                <FileText className="w-10 h-10 mb-3 text-primary" />
                <p className="mb-2 text-sm text-foreground">
                  <span className="font-semibold">{fileName}</span>
                </p>
                <p className="text-xs text-muted-foreground">Click to change file</p>
              </>
            ) : (
              <>
                <UploadCloud className="w-10 h-10 mb-3 text-muted-foreground group-hover:text-primary" />
                <p className="mb-2 text-sm text-muted-foreground">
                  <span className="font-semibold">Click to upload</span> or drag and drop
                </p>
                <p className="text-xs text-muted-foreground">PDF, PNG, JPG (MAX. 5MB)</p>
              </>
            )}
          </div>
          <Input id={id} type="file" className="hidden" onChange={handleFileChange} accept={accept} />
        </label>
      </div>
      {selectedFile && !error && (
        <div className="flex items-center text-xs text-green-600 mt-1">
          <CheckCircle className="w-4 h-4 mr-1" />
          File ready: {fileName} ({(selectedFile.size / 1024).toFixed(2)} KB)
        </div>
      )}
      {error && (
        <div className="flex items-center text-xs text-destructive mt-1">
          <AlertCircle className="w-4 h-4 mr-1" />
          {error}
        </div>
      )}
    </div>
  );
}