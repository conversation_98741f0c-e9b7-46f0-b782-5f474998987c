@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 25% 95%; /* Light Grey */
    --foreground: 210 10% 23%; /* Dark Grey for text */

    --card: 210 25% 100%; /* White or slightly off-white */
    --card-foreground: 210 10% 23%;

    --popover: 210 25% 100%;
    --popover-foreground: 210 10% 23%;

    --primary: 207 88% 68%; /* Calm Blue */
    --primary-foreground: 210 40% 98%; /* Light color for text on primary */

    --secondary: 210 20% 88%; /* Lighter grey for secondary elements */
    --secondary-foreground: 210 10% 23%;

    --muted: 210 20% 85%;
    --muted-foreground: 210 10% 45%;

    --accent: 174 44% 51%; /* Muted Teal */
    --accent-foreground: 174 40% 98%; /* Light color for text on accent */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 210 20% 80%;
    --input: 210 20% 88%;
    --ring: 207 88% 60%; /* Slightly darker/more saturated primary for focus rings */

    --radius: 0.5rem;

    /* Chart colors (can be adjusted if charts are used) */
    --chart-1: 207 80% 60%;
    --chart-2: 174 40% 45%;
    --chart-3: 210 30% 50%;
    --chart-4: 25 80% 60%;
    --chart-5: 280 70% 65%;
    
    /* Sidebar specific colors - may not be heavily used if no persistent sidebar */
    --sidebar-background: 210 25% 98%;
    --sidebar-foreground: 210 10% 26%;
    --sidebar-primary: 207 88% 60%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 174 44% 51%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 210 20% 85%;
    --sidebar-ring: 207 88% 55%;
  }

  .dark {
    --background: 210 20% 9%;
    --foreground: 210 40% 98%;

    --card: 210 20% 12%;
    --card-foreground: 210 40% 98%;

    --popover: 210 20% 9%;
    --popover-foreground: 210 40% 98%;

    --primary: 207 80% 64%;
    --primary-foreground: 207 80% 10%;

    --secondary: 210 20% 15%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 20% 15%;
    --muted-foreground: 210 40% 60%;

    --accent: 174 40% 50%;
    --accent-foreground: 174 40% 10%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 98%;

    --border: 210 20% 20%;
    --input: 210 20% 20%;
    --ring: 207 80% 56%;
    
    /* Chart colors for dark mode */
    --chart-1: 207 70% 55%;
    --chart-2: 174 35% 40%;
    --chart-3: 210 25% 45%;
    --chart-4: 25 70% 55%;
    --chart-5: 280 60% 60%;

    /* Sidebar specific colors for dark mode */
    --sidebar-background: 210 20% 10%;
    --sidebar-foreground: 210 40% 95%;
    --sidebar-primary: 207 80% 64%;
    --sidebar-primary-foreground: 207 80% 10%;
    --sidebar-accent: 174 40% 50%;
    --sidebar-accent-foreground: 174 40% 10%;
    --sidebar-border: 210 20% 20%;
    --sidebar-ring: 207 80% 56%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
    font-family: var(--font-inter), sans-serif;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply text-foreground;
  }
}
