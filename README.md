# CertiProof - Decentralized Certificate Platform (Frontend)

This is the Next.js frontend for CertiProof, a platform for issuing and verifying university certificates on the blockchain.

## Features

*   **Wallet Integration**: Connects with MetaMask for blockchain interactions.
*   **Admin Dashboard**: Allows authorized university admins to issue new certificates.
    *   Upload certificate document (PDF/image).
    *   Enter student details (name, wallet address).
    *   Mint certificate (simulated interaction with IPFS and smart contract).
*   **Student View**: Students can view certificates issued to their wallet address.
*   **Certificate Verification**: Anyone can verify a certificate's authenticity using its unique ID/hash or the student's wallet address.
*   **QR Code Generation**: (Placeholder) Certificates can display QR codes for easy verification.

## Tech Stack (Frontend)

*   **Next.js**: React framework for server-side rendering and static site generation.
*   **TypeScript**: For type safety and improved developer experience.
*   **Tailwind CSS**: Utility-first CSS framework for styling.
*   **ShadCN UI**: Re-usable UI components.
*   **Ethers.js** (Simulated): For interacting with Ethereum-compatible blockchains (interactions are mocked in this version).
*   **IPFS** (Simulated): For decentralized file storage (uploads are mocked).

## Project Structure

This frontend application is designed to be part of a larger project typically structured as:

```
certiproof/
├── frontend/         <-- This Next.js application
├── smart-contracts/  <-- Solidity smart contracts (e.g., Hardhat project)
├── scripts/          <-- Deployment and interaction scripts
└── README.md         <-- Main project README
```

## Prerequisites

*   Node.js (v18 or later recommended)
*   npm or yarn
*   MetaMask browser extension

## Getting Started

1.  **Clone the repository** (assuming this frontend is part of the main CertiProof repo):
    ```bash
    git clone <repository_url>
    cd certiproof/frontend
    ```

2.  **Install dependencies**:
    ```bash
    npm install
    # or
    yarn install
    ```

3.  **Set up Environment Variables**:
    Create a `.env.local` file in the `frontend` directory if needed for any specific API keys or configurations (e.g., Web3.Storage API token for actual IPFS uploads). This version uses mocked services, so no specific environment variables are strictly required to run it as is.

4.  **Configure Constants**:
    Open `src/lib/constants.ts`:
    *   Update `ADMIN_ADDRESSES` with the Ethereum wallet addresses (lowercase) that should have admin privileges on the platform.
    *   Once your smart contract is deployed, update `CERTIFICATE_CONTRACT_ABI` and `CERTIFICATE_CONTRACT_ADDRESS` with your actual contract details. The current ones are placeholders.

5.  **Run the development server**:
    ```bash
    npm run dev
    # or
    yarn dev
    ```
    The application will be available at `http://localhost:9002` (or the port specified in your `package.json` scripts).

## Using the Platform

1.  **Connect Wallet**: Click the "Connect Wallet" button in the header and connect using MetaMask. Ensure you are on the **Polygon Mumbai Testnet**.
2.  **Admin Dashboard (`/admin`)**:
    *   If your connected wallet address is in the `ADMIN_ADDRESSES` list (in `src/lib/constants.ts`), you will be able to access the admin functions.
    *   Fill out the form to issue a new certificate. Uploading to IPFS and minting are simulated.
3.  **Student View (`/student`)**:
    *   Enter a student's wallet address (or use your connected address) to search for their certificates.
4.  **Verifier Page (`/verify`)**:
    *   Verify a certificate by its ID/hash or by the student's wallet address.

## Smart Contract and IPFS Integration (Simulation)

This frontend version simulates interactions with a smart contract and IPFS:

*   **Smart Contract**: Functions in `src/lib/contractService.ts` mimic calls to a Solidity smart contract. You'll need to replace these with actual Ethers.js (or Viem) calls to your deployed contract.
*   **IPFS**: Functions in `src/lib/ipfsService.ts` simulate file uploads to IPFS. For a real implementation, you would integrate a service like Web3.Storage, Pinata, or run your own IPFS node.

## Building for Production

```bash
npm run build
npm start
# or
yarn build
yarn start
```

## Further Development

*   Implement actual smart contract interactions using Ethers.js or Viem.
*   Integrate a real IPFS upload solution (e.g., Web3.Storage SDK).
*   Enhance admin role management (e.g., role-based access control in the smart contract).
*   Implement listening to smart contract events to update UI in real-time.
*   Add more comprehensive error handling and loading states.
*   Develop the `smart-contracts/` and `scripts/` parts of the project.
*   Write unit and integration tests.

## Contributing

(Add contribution guidelines if this were an open project).
```