
"use client";

import { useWallet } from '@/contexts/WalletContext';
import { Button } from '@/components/ui/button';
import { LogIn, LogOut, UserCircle, AlertTriangle, Loader2 } from 'lucide-react';
import { useState, useEffect } from 'react';

export default function WalletConnectButton() {
  const { account, isLoading, error, connectWallet, disconnectWallet, chainId } = useWallet();
  const [mounted, setMounted] = useState(false);
  const POLYGON_MUMBAI_CHAIN_ID = 80001; // Defined in constants as well

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <Button disabled variant="outline" className="w-full sm:w-auto">
        <LogIn className="mr-2 h-4 w-4" />
        Connect Wallet
      </Button>
    );
  }

  if (isLoading) {
    return (
      <Button disabled variant="outline" className="w-full sm:w-auto">
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        Connecting...
      </Button>
    );
  }

  if (account) {
    const wrongNetwork = chainId !== POLYGON_MUMBAI_CHAIN_ID;
    return (
      <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-2">
        {wrongNetwork && (
           <span className="flex items-center text-destructive text-xs sm:text-sm" title="Please switch to Polygon Mumbai Testnet">
             <AlertTriangle className="h-4 w-4 mr-1" />
             Wrong Network
           </span>
        )}
        <Button variant="outline" size="sm" className="w-full sm:w-auto">
          <UserCircle className="mr-2 h-4 w-4" />
          {`${account.substring(0, 6)}...${account.substring(account.length - 4)}`}
        </Button>
        <Button onClick={disconnectWallet} variant="destructive" size="sm" className="w-full sm:w-auto">
          <LogOut className="mr-2 h-4 w-4" />
          Disconnect
        </Button>
      </div>
    );
  }

  return (
    <Button onClick={connectWallet} className="w-full sm:w-auto bg-accent hover:bg-accent/90 text-accent-foreground">
      <LogIn className="mr-2 h-4 w-4" />
      Connect Wallet
    </Button>
  );
}
