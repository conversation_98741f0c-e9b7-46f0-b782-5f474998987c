
"use client";

import { useState, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import FileUploadInput from '@/components/FileUploadInput';
import { useWallet } from '@/contexts/WalletContext';
import { useToast } from '@/hooks/use-toast';
import { uploadFileToIPFS, uploadMetadataToIPFS } from '@/lib/ipfsService';
import { issueCertificate as contractIssueCertificate, getAllCertificates as contractGetAllCertificates } from '@/lib/contractService';
import { ISSUER_NAME, POLYGON_MUMBAI_CHAIN_ID } from '@/lib/constants';
import type { Certificate } from '@/types';
import CertificateCard from '@/components/CertificateCard';
import { Loader2, ShieldAlert, ListChecks } from 'lucide-react';

const issueCertificateSchema = z.object({
  studentName: z.string().min(2, { message: "Student name must be at least 2 characters." }),
  studentAddress: z.string().regex(/^0x[a-fA-F0-9]{40}$/, { message: "Invalid Ethereum address." }),
  certificateTitle: z.string().min(5, { message: "Certificate title must be at least 5 characters." }),
});

type IssueCertificateFormValues = z.infer<typeof issueCertificateSchema>;

export default function AdminPage() {
  const { account, signer, provider, isAdmin, isLoading: walletLoading, chainId } = useWallet();
  const { toast } = useToast();
  const [mounted, setMounted] = useState(false);
  const [certificateFile, setCertificateFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [issuedCertificates, setIssuedCertificates] = useState<Certificate[]>([]);
  const [isLoadingCerts, setIsLoadingCerts] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const form = useForm<IssueCertificateFormValues>({
    resolver: zodResolver(issueCertificateSchema),
    defaultValues: {
      studentName: '',
      studentAddress: '',
      certificateTitle: '',
    },
  });

  const fetchIssuedCertificates = async () => {
    if (!provider) return;
    setIsLoadingCerts(true);
    try {
      // Fetch all certificates (in a real app, this would be filtered by issuer)
      const certs = await contractGetAllCertificates(provider);
      setIssuedCertificates(certs);
      console.log('Admin fetched all certificates:', certs);
    } catch (error: any) {
      toast({ title: 'Error fetching certificates', description: error.message, variant: 'destructive' });
    } finally {
      setIsLoadingCerts(false);
    }
  };

  useEffect(() => {
    if (mounted && isAdmin && provider) {
      fetchIssuedCertificates();
    }
  }, [isAdmin, provider, mounted]);


  const onSubmit: SubmitHandler<IssueCertificateFormValues> = async (data) => {
    if (!certificateFile) {
      toast({ title: 'File Missing', description: 'Please select a certificate file to upload.', variant: 'destructive' });
      return;
    }
    if (!signer) {
      toast({ title: 'Wallet Error', description: 'Signer not available. Please ensure your wallet is connected correctly.', variant: 'destructive' });
      return;
    }

    setIsSubmitting(true);
    try {
      toast({ title: 'Processing...', description: 'Uploading certificate file to IPFS...' });
      const fileIpfsHash = await uploadFileToIPFS(certificateFile);
      toast({ title: 'File Uploaded', description: `File CID: ${fileIpfsHash.substring(0,10)}...` });

      const metadata = {
        name: data.certificateTitle,
        description: `Certificate of ${data.certificateTitle} issued to ${data.studentName} by ${ISSUER_NAME}.`,
        image: `ipfs://${fileIpfsHash}`, 
        attributes: [
          { trait_type: "Student Name", value: data.studentName },
          { trait_type: "Student Address", value: data.studentAddress },
          { trait_type: "Certificate Title", value: data.certificateTitle },
          { trait_type: "Issuer", value: ISSUER_NAME },
          { trait_type: "Issue Date", value: new Date().toISOString() },
        ],
      };

      toast({ title: 'Processing...', description: 'Uploading metadata to IPFS...' });
      const metadataIpfsUrl = `ipfs://${await uploadMetadataToIPFS(metadata)}`;
      toast({ title: 'Metadata Uploaded', description: `Metadata URL: ${metadataIpfsUrl.substring(0,15)}...` });

      toast({ title: 'Processing...', description: 'Please confirm the transaction in your wallet to mint the certificate.' });
      const { transactionHash, certificateId } = await contractIssueCertificate(
        signer,
        data.studentAddress,
        data.studentName, 
        data.certificateTitle, 
        metadataIpfsUrl,
        fileIpfsHash // Pass the document's IPFS hash
      );

      toast({
        title: 'Certificate Issued!',
        description: (
          <div>
            <p>Transaction Hash: {transactionHash.substring(0,10)}...</p>
            <p>Certificate ID: {certificateId.substring(0,10)}...</p>
          </div>
        ),
      });
      form.reset();
      setCertificateFile(null);
      // Attempt to clear the visual selection in FileUploadInput if it manages its own state
      // This might require a ref or a prop to reset FileUploadInput externally
      const fileInput = document.getElementById('certificate-file') as HTMLInputElement | null;
      if (fileInput) fileInput.value = '';

      if (mounted && isAdmin && provider) fetchIssuedCertificates();

    } catch (error: any) {
      console.error("Error issuing certificate:", error);
      toast({ title: 'Issuance Failed', description: error.message || 'An unknown error occurred.', variant: 'destructive' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!mounted || walletLoading) {
    return <div className="flex justify-center items-center min-h-[60vh]"><Loader2 className="h-12 w-12 animate-spin text-primary" /> <span className="ml-4 text-xl">Loading...</span></div>;
  }

  if (!account) {
    return (
      <Card className="max-w-md mx-auto text-center shadow-xl">
        <CardHeader>
          <CardTitle className="font-headline">Admin Access Required</CardTitle>
        </CardHeader>
        <CardContent>
          <ShieldAlert className="h-16 w-16 text-destructive mx-auto mb-4" />
          <p className="text-muted-foreground">Please connect your wallet to access the admin dashboard.</p>
        </CardContent>
        <CardFooter className="justify-center">
           <p className="text-xs text-muted-foreground">Use the Connect Wallet button in the header.</p>
        </CardFooter>
      </Card>
    );
  }

  if (chainId !== POLYGON_MUMBAI_CHAIN_ID) {
    return (
      <Card className="max-w-md mx-auto text-center shadow-xl">
        <CardHeader>
          <CardTitle className="font-headline">Incorrect Network</CardTitle>
        </CardHeader>
        <CardContent>
          <ShieldAlert className="h-16 w-16 text-destructive mx-auto mb-4" />
          <p className="text-muted-foreground">Please switch to the Polygon Mumbai Testnet in your wallet to use the admin features.</p>
        </CardContent>
      </Card>
    );
  }

  if (!mounted || !isAdmin) {
    return (
      <Card className="max-w-md mx-auto text-center shadow-xl">
        <CardHeader>
          <CardTitle className="font-headline">Unauthorized Access</CardTitle>
        </CardHeader>
        <CardContent>
          <ShieldAlert className="h-16 w-16 text-destructive mx-auto mb-4" />
          <p className="text-muted-foreground">The connected wallet ({`${account.substring(0, 6)}...${account.substring(account.length-4)}`}) does not have admin privileges.</p>
        </CardContent>
      </Card>
    );
  }


  return (
    <div className="space-y-8">
      <section>
        <h1 className="text-3xl font-bold mb-2 font-headline">Admin Dashboard</h1>
        <p className="text-muted-foreground">Issue new university certificates to students.</p>
      </section>

      <Card className="shadow-xl">
        <CardHeader>
          <CardTitle className="font-headline">Issue New Certificate</CardTitle>
          <CardDescription>Fill in the details below to mint a new certificate.</CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="studentName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Student Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Ada Lovelace" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="studentAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Student Wallet Address</FormLabel>
                    <FormControl>
                      <Input placeholder="0x..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="certificateTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Certificate Title</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Bachelor of Computer Science" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormItem>
                <FileUploadInput 
                  onFileSelect={setCertificateFile} 
                  key={certificateFile ? 'file-selected' : 'no-file'} // Force re-render when file is cleared
                />
                 {!certificateFile && form.formState.isSubmitted && (
                    <p className="text-sm font-medium text-destructive">Certificate document is required.</p>
                  )}
              </FormItem>
            </CardContent>
            <CardFooter>
              <Button type="submit" disabled={isSubmitting} className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground">
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Issuing Certificate...
                  </>
                ) : (
                  'Issue Certificate'
                )}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>

      <section className="mt-12">
        <h2 className="text-2xl font-bold mb-4 font-headline flex items-center">
          <ListChecks className="mr-3 h-7 w-7 text-primary" />
          All Issued Certificates
        </h2>
        {isLoadingCerts ? (
          <div className="flex justify-center items-center py-8"><Loader2 className="h-8 w-8 animate-spin text-primary" /> <span className="ml-3">Loading certificates...</span></div>
        ) : issuedCertificates.length > 0 ? (
          <div className="grid md:grid-cols-2 gap-6">
            {issuedCertificates.map((cert) => (
              <CertificateCard key={cert.id} certificate={cert} />
            ))}
          </div>
        ) : (
          <p className="text-muted-foreground">
            No certificates have been issued yet. Issue your first certificate using the form above.
          </p>
        )}
      </section>
    </div>
  );
}
