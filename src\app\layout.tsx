
import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import Header from '@/components/shared/Header';
import { WalletProvider } from '@/contexts/WalletContext';
import { CurrentYear } from '@/components/shared/CurrentYear';

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' });

export const metadata: Metadata = {
  title: 'CertiProof - Decentralized Certificate Platform',
  description: 'Issue and verify university certificates on the blockchain.',
  icons: null, // Explicitly disable automatic favicon handling
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} h-full`}>
      <head>
        {/* Google Fonts CDN links are managed by project guidelines, Inter is loaded via next/font */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Clean up browser extension attributes before React hydration
              (function() {
                if (typeof window !== 'undefined') {
                  // Remove common browser extension attributes that cause hydration mismatches
                  const extensionAttributes = [
                    'inmaintabuse',
                    'data-new-gr-c-s-check-loaded',
                    'data-gr-ext-installed',
                    'cz-shortcut-listen',
                    'data-lt-installed'
                  ];

                  function cleanupAttributes() {
                    extensionAttributes.forEach(attr => {
                      if (document.body && document.body.hasAttribute(attr)) {
                        document.body.removeAttribute(attr);
                      }
                      if (document.documentElement && document.documentElement.hasAttribute(attr)) {
                        document.documentElement.removeAttribute(attr);
                      }
                    });
                  }

                  // Clean up immediately
                  if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', cleanupAttributes);
                  } else {
                    cleanupAttributes();
                  }

                  // Also clean up before React hydration
                  if (document.readyState !== 'complete') {
                    window.addEventListener('load', cleanupAttributes);
                  }
                }
              })();
            `,
          }}
        />
      </head>
      <body className="font-body flex flex-col min-h-screen bg-background" suppressHydrationWarning={true}>
        <WalletProvider>
          <Header />
          <main className="flex-grow container mx-auto px-4 py-8">
            {children}
          </main>
          <footer className="bg-muted/50 text-muted-foreground py-6 text-center text-sm">
            <div className="container mx-auto px-4">
              © <CurrentYear /> CertiProof. All rights reserved.
              <p className="text-xs mt-1">Decentralized Certificate Platform</p>
            </div>
          </footer>
          <Toaster />
        </WalletProvider>
      </body>
    </html>
  );
}
