
"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import WalletConnectButton from '@/components/WalletConnectButton';
import { Award } from 'lucide-react';
import { useWallet } from '@/contexts/WalletContext';
import { useState, useEffect } from 'react';

export default function Header() {
  const { account, isAdmin } = useWallet();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Define all possible navigation items
  const navConfig = [
    { label: 'Home', href: '/', alwaysShow: true },
    { label: 'Admin', href: '/admin', showIf: () => mounted && isAdmin },
    { label: 'Student', href: '/student', alwaysShow: true },
    { label: 'Verify', href: '/verify', alwaysShow: true },
    { label: 'Login', href: '/login', dynamicLabelTarget: true } // Label will be Login/Account Status
  ];

  return (
    <header className="bg-card shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4 py-3 flex flex-col sm:flex-row justify-between items-center">
        <Link href="/" className="flex items-center space-x-2 text-primary hover:text-primary/80 transition-colors mb-2 sm:mb-0">
          <Award className="h-8 w-8" />
          <span className="text-2xl font-headline font-semibold">CertiProof</span>
        </Link>
        <nav className="flex-grow sm:flex-grow-0 flex flex-wrap justify-center md:flex items-center space-x-1 sm:space-x-2 lg:space-x-4 mb-2 sm:mb-0">
          {navConfig.map((item) => {
            if (item.showIf && !item.showIf()) {
              return null; // Don't render if condition not met
            }

            let displayLabel = item.label;
            if (item.dynamicLabelTarget) {
              displayLabel = (mounted && account) ? "Account Status" : "Login";
            }

            if (item.alwaysShow || (item.showIf && item.showIf())) {
              return (
                <Button key={item.label} variant="ghost" asChild size="sm">
                  <Link href={item.href}>{displayLabel}</Link>
                </Button>
              );
            }
            return null;
          })}
        </nav>
        <div className="flex items-center space-x-2">
          <WalletConnectButton />
          {mounted && account && (
            <span className="text-xs sm:text-sm text-muted-foreground hidden md:inline">
              {isAdmin ? '(Admin)' : '(Student)'}
            </span>
          )}
        </div>
      </div>
    </header>
  );
}
