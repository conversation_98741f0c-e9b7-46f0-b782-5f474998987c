
"use client";

import { useWallet } from '@/contexts/WalletContext';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import Link from 'next/link';
import { LogIn, UserCheck, Shield, User, Loader2, AlertTriangle } from 'lucide-react';
import { POLYGON_MUMBAI_CHAIN_ID } from '@/lib/constants';
import { useState, useEffect } from 'react';

export default function LoginPage() {
  const { account, isAdmin, connectWallet, isLoading, chainId } = useWallet();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <span className="ml-4 text-xl">Loading...</span>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <span className="ml-4 text-xl">Loading Wallet Information...</span>
      </div>
    );
  }

  if (!account) {
    return (
      <Card className="max-w-md mx-auto text-center shadow-xl">
        <CardHeader>
          <CardTitle className="font-headline text-2xl">Login to CertiProof</CardTitle>
          <CardDescription>Connect your wallet to access platform features.</CardDescription>
        </CardHeader>
        <CardContent>
          <LogIn className="h-16 w-16 text-primary mx-auto mb-6" />
          <Button onClick={connectWallet} className="w-full bg-primary hover:bg-primary/90 text-primary-foreground">
            <LogIn className="mr-2 h-5 w-5" /> Connect Wallet
          </Button>
        </CardContent>
        <CardFooter className="justify-center">
          <p className="text-xs text-muted-foreground">
            Ensure you are on the Polygon Mumbai Testnet.
          </p>
        </CardFooter>
      </Card>
    );
  }

  // Wallet is connected
  if (chainId !== POLYGON_MUMBAI_CHAIN_ID) {
    return (
      <Card className="max-w-md mx-auto text-center shadow-xl">
        <CardHeader>
          <CardTitle className="font-headline text-2xl">Incorrect Network</CardTitle>
        </CardHeader>
        <CardContent>
          <AlertTriangle className="h-16 w-16 text-destructive mx-auto mb-4" />
          <p className="text-muted-foreground">
            Your wallet is connected, but you are on the wrong network.
            Please switch to the Polygon Mumbai Testnet to proceed.
          </p>
          <p className="text-sm mt-2">Connected Address: <br/><span className="font-mono text-xs break-all">{account}</span></p>
        </CardContent>
         <CardFooter className="justify-center">
          <p className="text-xs text-muted-foreground">
            Refresh this page after switching networks.
          </p>
        </CardFooter>
      </Card>
    );
  }

  // Wallet connected and on correct network
  return (
    <div className="space-y-6">
      <Card className="max-w-lg mx-auto text-center shadow-xl">
        <CardHeader>
          <UserCheck className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <CardTitle className="font-headline text-2xl">Wallet Connected!</CardTitle>
          <CardDescription>
            Address: <span className="font-mono text-sm break-all">{account}</span>
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {mounted && isAdmin ? (
            <>
              <Shield className="h-12 w-12 text-primary mx-auto mb-2" />
              <h2 className="text-xl font-semibold">Admin Access Granted</h2>
              <p className="text-muted-foreground">
                You are recognized as an administrator.
              </p>
              <Button asChild className="w-full bg-primary hover:bg-primary/90 text-primary-foreground">
                <Link href="/admin">Go to Admin Dashboard</Link>
              </Button>
            </>
          ) : (
            <>
              <User className="h-12 w-12 text-primary mx-auto mb-2" />
              <h2 className="text-xl font-semibold">Student Access</h2>
              <p className="text-muted-foreground">
                Access your certificates or verify existing ones.
              </p>
              <Button asChild className="w-full bg-primary hover:bg-primary/90 text-primary-foreground">
                <Link href="/student">View My Certificates</Link>
              </Button>
            </>
          )}
          <Button variant="outline" asChild className="w-full">
            <Link href="/verify">Verify a Certificate</Link>
          </Button>
        </CardContent>
        <CardFooter className="justify-center">
          <p className="text-xs text-muted-foreground">
            You can disconnect your wallet using the button in the header.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
