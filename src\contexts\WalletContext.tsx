
"use client";

import type { WalletContextType, WalletState } from '@/types';
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { ethers } from 'ethers'; // Assuming ethers can be imported. If not, direct window.ethereum use.
import { useToast } from '@/hooks/use-toast';
import { ADMIN_ADDRESSES, POLYGON_MUMBAI_CHAIN_ID } from '@/lib/constants';

const initialState: WalletState = {
  account: null,
  provider: null,
  signer: null,
  chainId: null,
  isAdmin: false,
  isLoading: false,
  error: null,
};

const WalletContext = createContext<WalletContextType | undefined>(undefined);

export const WalletProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<WalletState>(initialState);
  const [mounted, setMounted] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleAccountsChanged = useCallback((accounts: string[]) => {
    if (accounts.length === 0) {
      toast({ title: 'Disconnected', description: 'MetaMask account disconnected.', variant: 'destructive' });
      disconnectWallet();
    } else {
      const newAccount = accounts[0];
      setState(prevState => ({ ...prevState, account: newAccount }));
      checkAdminStatus(newAccount);
      toast({ title: 'Account Changed', description: `Switched to account: ${newAccount.substring(0,6)}...${newAccount.substring(newAccount.length-4)}` });
    }
  }, [toast]);

  const handleChainChanged = useCallback((newChainId: string) => {
    const parsedChainId = parseInt(newChainId, 16);
    setState(prevState => ({ ...prevState, chainId: parsedChainId }));
    if (parsedChainId !== POLYGON_MUMBAI_CHAIN_ID) {
      toast({ title: 'Wrong Network', description: 'Please connect to Polygon Mumbai Testnet.', variant: 'destructive'});
    } else {
       toast({ title: 'Network Changed', description: 'Connected to Polygon Mumbai Testnet.'});
    }
  }, [toast]);

  const connectWallet = async () => {
    if (!mounted || typeof window === 'undefined' || typeof window.ethereum === 'undefined') {
      toast({ title: 'Error', description: 'MetaMask is not installed. Please install it to continue.', variant: 'destructive' });
      setState(prevState => ({ ...prevState, error: 'MetaMask not installed.' }));
      return;
    }

    setState(prevState => ({ ...prevState, isLoading: true, error: null }));
    try {
      const provider = new ethers.BrowserProvider(window.ethereum);
      const accounts = await provider.send('eth_requestAccounts', []);
      
      if (accounts.length > 0) {
        const signer = await provider.getSigner();
        const network = await provider.getNetwork();
        const account = accounts[0];

        setState({
          account,
          provider,
          signer,
          chainId: Number(network.chainId),
          isAdmin: ADMIN_ADDRESSES.includes(account.toLowerCase()),
          isLoading: false,
          error: null,
        });

        if (Number(network.chainId) !== POLYGON_MUMBAI_CHAIN_ID) {
          toast({ title: 'Wrong Network', description: 'Please switch to Polygon Mumbai Testnet in MetaMask.', variant: 'destructive' });
        } else {
          toast({ title: 'Wallet Connected', description: `Connected as ${account.substring(0,6)}...${account.substring(account.length-4)}` });
        }

        if (mounted && window.ethereum) {
          window.ethereum.on('accountsChanged', handleAccountsChanged);
          window.ethereum.on('chainChanged', handleChainChanged);
        }

      } else {
         throw new Error("No accounts found.");
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to connect wallet.';
      toast({ title: 'Connection Failed', description: errorMessage, variant: 'destructive' });
      setState(prevState => ({ ...prevState, isLoading: false, error: errorMessage }));
    }
  };

  const disconnectWallet = () => {
    setState(initialState);
    if (mounted && typeof window !== 'undefined' && window.ethereum) {
      window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
      window.ethereum.removeListener('chainChanged', handleChainChanged);
    }
    toast({ title: 'Wallet Disconnected', description: 'You have been disconnected.' });
  };

  const checkAdminStatus = (address: string) => {
    setState(prevState => ({ ...prevState, isAdmin: ADMIN_ADDRESSES.includes(address.toLowerCase()) }));
  };
  
  useEffect(() => {
    // Cleanup listeners when the component unmounts or provider changes
    return () => {
      if (mounted && typeof window !== 'undefined' && window.ethereum) {
        window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
        window.ethereum.removeListener('chainChanged', handleChainChanged);
      }
    };
  }, [handleAccountsChanged, handleChainChanged, mounted]);


  return (
    <WalletContext.Provider value={{ ...state, connectWallet, disconnectWallet, checkAdminStatus }}>
      {children}
    </WalletContext.Provider>
  );
};

export const useWallet = (): WalletContextType => {
  const context = useContext(WalletContext);
  if (context === undefined) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
};
