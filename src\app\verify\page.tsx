
"use client";

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useWallet } from '@/contexts/WalletContext'; // To get provider
import { useToast } from '@/hooks/use-toast';
import { getCertificateById, getCertificatesByOwner } from '@/lib/contractService';
import type { Certificate } from '@/types';
import CertificateCard from '@/components/CertificateCard';
import { Loader2, Search, ShieldCheck, ShieldOff, FileSearch, UserSearch } from 'lucide-react';

type VerificationResult = {
  status: 'verified' | 'not_found' | 'error' | 'multiple_found';
  message: string;
  certificate?: Certificate | null;
  certificates?: Certificate[];
};

export default function VerifyPage() {
  const { provider } = useWallet();
  const { toast } = useToast();
  const searchParams = useSearchParams();

  const [mounted, setMounted] = useState(false);
  const [searchId, setSearchId] = useState('');
  const [searchAddress, setSearchAddress] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [activeTab, setActiveTab] = useState("id");

  useEffect(() => {
    setMounted(true);
  }, []);


  useEffect(() => {
    if (!mounted) return;

    const idFromQuery = searchParams.get('id');
    if (idFromQuery) {
      setSearchId(idFromQuery);
      setActiveTab("id");
      // Check if provider is available before calling, or handle inside function
      if (provider) {
        handleVerifyById(idFromQuery);
      } else {
        // Optionally toast that wallet needs to be connected for auto-verify
        // console.warn("Provider not available for auto-verification. Connect wallet.");
        // toast({ title: 'Wallet Needed', description: 'Connect wallet for automatic verification.', variant: 'default' });
      }
    }
  }, [searchParams, provider, mounted]); // Added mounted to dependency array

  const handleVerifyById = async (idToSearch: string) => {
    if (!idToSearch) {
      toast({ title: 'Missing ID', description: 'Please enter a Certificate ID.', variant: 'destructive' });
      return;
    }
    if (!provider) {
        toast({ title: 'Provider Missing', description: 'Wallet provider is not available. Please connect your wallet.', variant: 'destructive' });
        return;
    }

    setIsLoading(true);
    setVerificationResult(null);
    try {
      const certificate = await getCertificateById(provider, idToSearch);
      if (certificate) {
        setVerificationResult({ status: 'verified', message: 'Certificate is authentic and found.', certificate });
        toast({ title: 'Verification Success', description: 'Certificate found and verified.' });
      } else {
        setVerificationResult({ status: 'not_found', message: 'Certificate not found with this ID.' });
        toast({ title: 'Verification Failed', description: 'No certificate found for the given ID.', variant: 'destructive' });
      }
    } catch (error: any) {
      setVerificationResult({ status: 'error', message: error.message || 'An error occurred during verification.' });
      toast({ title: 'Verification Error', description: error.message, variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyByAddress = async (addressToSearch: string) => {
    if (!addressToSearch) {
      toast({ title: 'Missing Address', description: 'Please enter a Student Wallet Address.', variant: 'destructive' });
      return;
    }
     if (!provider) {
        toast({ title: 'Provider Missing', description: 'Wallet provider is not available. Please connect your wallet.', variant: 'destructive' });
        return;
    }

    setIsLoading(true);
    setVerificationResult(null);
    try {
      const certificates = await getCertificatesByOwner(provider, addressToSearch);
      if (certificates.length > 0) {
        setVerificationResult({ status: 'multiple_found', message: `Found ${certificates.length} certificate(s) for this address.`, certificates });
        toast({ title: 'Certificates Found', description: `Displayed ${certificates.length} certificate(s).` });
      } else {
        setVerificationResult({ status: 'not_found', message: 'No certificates found for this address.' });
        toast({ title: 'Verification Failed', description: 'No certificates found for the given address.', variant: 'destructive' });
      }
    } catch (error: any) {
      setVerificationResult({ status: 'error', message: error.message || 'An error occurred during verification.' });
      toast({ title: 'Verification Error', description: error.message, variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitById = (e: React.FormEvent) => {
    e.preventDefault();
    handleVerifyById(searchId);
  };

  const handleSubmitByAddress = (e: React.FormEvent) => {
    e.preventDefault();
    handleVerifyByAddress(searchAddress);
  };

  return (
    <div className="space-y-8">
      <section>
        <h1 className="text-3xl font-bold mb-2 font-headline">Verify Certificate</h1>
        <p className="text-muted-foreground">Check the authenticity of a certificate using its ID or the student's wallet address.</p>
      </section>

      <Card className="shadow-xl">
        <CardHeader>
          <CardTitle className="font-headline">Verification Tool</CardTitle>
          <CardDescription>Select verification method and enter the required information.</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="id">
                <FileSearch className="mr-2 h-4 w-4" /> By Certificate ID
              </TabsTrigger>
              <TabsTrigger value="address">
                <UserSearch className="mr-2 h-4 w-4" /> By Student Address
              </TabsTrigger>
            </TabsList>
            <TabsContent value="id" className="mt-6">
              <form onSubmit={handleSubmitById} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="certificateId">Certificate ID / Hash</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="certificateId"
                      type="text"
                      placeholder="Enter Certificate ID or Transaction Hash"
                      value={searchId}
                      onChange={(e) => setSearchId(e.target.value)}
                      className="flex-grow"
                    />
                    <Button type="submit" disabled={isLoading} className="bg-primary hover:bg-primary/90 text-primary-foreground">
                      {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Search className="mr-2 h-4 w-4" />}
                      Verify
                    </Button>
                  </div>
                </div>
              </form>
            </TabsContent>
            <TabsContent value="address" className="mt-6">
              <form onSubmit={handleSubmitByAddress} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="studentWalletAddress">Student Wallet Address</Label>
                   <div className="flex space-x-2">
                    <Input
                      id="studentWalletAddress"
                      type="text"
                      placeholder="0x..."
                      value={searchAddress}
                      onChange={(e) => setSearchAddress(e.target.value)}
                      className="flex-grow"
                    />
                    <Button type="submit" disabled={isLoading} className="bg-primary hover:bg-primary/90 text-primary-foreground">
                      {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Search className="mr-2 h-4 w-4" />}
                      Verify
                    </Button>
                  </div>
                </div>
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {isLoading && (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="ml-4 text-lg">Verifying Certificate...</p>
        </div>
      )}

      {verificationResult && !isLoading && (
        <Card className={`shadow-lg ${verificationResult.status === 'verified' || verificationResult.status === 'multiple_found' ? 'border-green-500' : 'border-destructive'}`}>
          <CardHeader className="flex flex-row items-center space-x-3">
            {verificationResult.status === 'verified' || verificationResult.status === 'multiple_found' ? (
              <ShieldCheck className="h-8 w-8 text-green-500" />
            ) : (
              <ShieldOff className="h-8 w-8 text-destructive" />
            )}
            <CardTitle className={`font-headline ${verificationResult.status === 'verified' || verificationResult.status === 'multiple_found' ? 'text-green-600' : 'text-destructive'}`}>
              Verification Result
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg mb-4">{verificationResult.message}</p>
            {verificationResult.status === 'verified' && verificationResult.certificate && (
              <CertificateCard certificate={verificationResult.certificate} />
            )}
            {verificationResult.status === 'multiple_found' && verificationResult.certificates && (
              <div className="space-y-4 mt-4">
                <h3 className="text-xl font-semibold">Certificates Found:</h3>
                {verificationResult.certificates.map(cert => (
                  <CertificateCard key={cert.id} certificate={cert} />
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
