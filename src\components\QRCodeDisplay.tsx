
"use client";

import Image from 'next/image';

interface QRCodeDisplayProps {
  qrCodeUrl: string;
  altText?: string;
  size?: number;
}

export default function QRCodeDisplay({ qrCodeUrl, altText = "Certificate QR Code", size = 200 }: QRCodeDisplayProps) {
  if (!qrCodeUrl) return null;

  return (
    <div className="flex flex-col items-center p-4 bg-muted/30 rounded-md">
      <Image
        src={qrCodeUrl}
        alt={altText}
        width={size}
        height={size}
        className="rounded-md shadow-md"
        data-ai-hint="qr code"
      />
    </div>
  );
}
