"use client";

import type { Certificate } from '@/types';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Award, CalendarDays, User, Hash, ExternalLink, QrCode as QrCodeIcon, Download } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import QRCodeDisplay from './QRCodeDisplay';
import { IPFS_GATEWAY_PREFIX } from '@/lib/constants';
import { format } from 'date-fns';

interface CertificateCardProps {
  certificate: Certificate;
}

export default function CertificateCard({ certificate }: CertificateCardProps) {
  const [showQr, setShowQr] = useState(false);

  const certificateDocumentUrl = `${IPFS_GATEWAY_PREFIX}${certificate.certificateIpfsHash}`;

  return (
    <Card className="w-full shadow-lg hover:shadow-xl transition-shadow duration-300">
      <CardHeader>
        <div className="flex items-center space-x-3 mb-2">
          <Award className="h-8 w-8 text-primary" />
          <CardTitle className="font-headline text-xl md:text-2xl">{certificate.title}</CardTitle>
        </div>
        <CardDescription>Issued by: {certificate.issuerName}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3 text-sm">
        <div className="flex items-center">
          <User className="h-4 w-4 mr-2 text-muted-foreground" />
          <strong>Student:</strong>&nbsp;{certificate.studentName}
        </div>
        <div className="flex items-center">
          <User className="h-4 w-4 mr-2 text-muted-foreground" />
          <strong>Address:</strong>&nbsp;
          <span className="truncate" title={certificate.studentAddress}>
            {`${certificate.studentAddress.substring(0, 10)}...${certificate.studentAddress.substring(certificate.studentAddress.length - 8)}`}
          </span>
        </div>
        <div className="flex items-center">
          <CalendarDays className="h-4 w-4 mr-2 text-muted-foreground" />
          <strong>Issue Date:</strong>&nbsp;{format(new Date(certificate.issueDate), 'MMMM d, yyyy')}
        </div>
        <div className="flex items-center">
          <Hash className="h-4 w-4 mr-2 text-muted-foreground" />
          <strong>Certificate ID:</strong>&nbsp;
          <span className="truncate" title={certificate.id}>
            {`${certificate.id.substring(0, 10)}...${certificate.id.substring(certificate.id.length - 8)}`}
          </span>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" asChild>
            <a href={certificateDocumentUrl} target="_blank" rel="noopener noreferrer">
              <Download className="mr-2 h-4 w-4" /> View Document
            </a>
          </Button>
          <Button variant="outline" size="sm" onClick={() => setShowQr(!showQr)}>
            <QrCodeIcon className="mr-2 h-4 w-4" /> {showQr ? 'Hide' : 'Show'} QR
          </Button>
          <Button variant="link" size="sm" asChild className="p-0 h-auto">
            <Link href={`/verify?id=${certificate.id}`} className="text-primary">
              Verify Online <ExternalLink className="ml-1 h-3 w-3" />
            </Link>
          </Button>
        </div>
      </CardFooter>
      {showQr && certificate.qrCodeUrl && (
        <div className="p-4 border-t">
          <QRCodeDisplay qrCodeUrl={certificate.qrCodeUrl} altText={`QR Code for ${certificate.title}`} />
           <p className="text-xs text-muted-foreground mt-2 text-center">Scan to verify certificate ID: {certificate.id.substring(0,6)}...</p>
        </div>
      )}
    </Card>
  );
}