
import type { Certificate } from '@/types';
import { ISSUER_NAME, QR_CODE_API_URL, PUBLIC_VIEWABLE_IPFS_CID } from './constants';

const LOCAL_STORAGE_KEY = 'certiproof_certificates';

const generateQrCodeUrl = (id: string): string => {
  // Use a consistent URL for SSR/CSR compatibility
  const verificationUrl = `/verify?id=${id}`;
  return `${QR_CODE_API_URL}?size=200x200&data=${encodeURIComponent(verificationUrl)}`;
};

// Helper function to ensure a certificate uses the public viewable CID for its document
const ensurePublicDocumentCID = (cert: Certificate): Certificate => ({
  ...cert,
  certificateIpfsHash: PUBLIC_VIEWABLE_IPFS_CID, // Always use the public CID for the document
  qrCodeUrl: generateQrCodeUrl(cert.id) // Also ensure QR code is up-to-date
});

// Default certificates to load if localStorage is empty or invalid
const defaultMockCertificates: Certificate[] = [
  {
    id: 'txhash001_default',
    title: 'Bachelor of Science in Blockchain Technology',
    studentName: 'Alice Wonderland',
    studentAddress: '******************************************',
    issueDate: new Date('2023-05-20').toISOString(),
    issuerName: ISSUER_NAME,
    certificateIpfsHash: PUBLIC_VIEWABLE_IPFS_CID,
    metadataIpfsUrl: 'ipfs://QmMetadataHashAlice/metadata.json',
    qrCodeUrl: generateQrCodeUrl('txhash001_default'),
  },
  {
    id: 'txhash002_default',
    title: 'Master of Arts in Digital Humanities',
    studentName: 'Bob The Builder',
    studentAddress: '******************************************',
    issueDate: new Date('2023-11-10').toISOString(),
    issuerName: ISSUER_NAME,
    certificateIpfsHash: PUBLIC_VIEWABLE_IPFS_CID,
    metadataIpfsUrl: 'ipfs://QmMetadataHashBob/metadata.json',
    qrCodeUrl: generateQrCodeUrl('txhash002_default'),
  },
  {
    id: 'txhash003_default',
    title: 'Certificate in Advanced Cryptography',
    studentName: 'Carol Danvers',
    studentAddress: '******************************************',
    issueDate: new Date('2024-01-15').toISOString(),
    issuerName: ISSUER_NAME,
    certificateIpfsHash: PUBLIC_VIEWABLE_IPFS_CID,
    metadataIpfsUrl: 'ipfs://QmMetadataHashCarol/metadata.json',
    qrCodeUrl: generateQrCodeUrl('txhash003_default'),
  },
  // Add a certificate for the admin address for testing
  {
    id: 'txhash004_admin_test',
    title: 'Certificate in Smart Contract Development',
    studentName: 'Admin Test User',
    studentAddress: '******************************************', // Admin address from constants
    issueDate: new Date('2024-01-20').toISOString(),
    issuerName: ISSUER_NAME,
    certificateIpfsHash: PUBLIC_VIEWABLE_IPFS_CID,
    metadataIpfsUrl: 'ipfs://QmMetadataHashAdmin/metadata.json',
    qrCodeUrl: generateQrCodeUrl('txhash004_admin_test'),
  },
  // Add a certificate for the student address shown in the screenshot
  {
    id: 'txhash005_student_test',
    title: 'Certificate in Blockchain Fundamentals',
    studentName: 'Test Student',
    studentAddress: '0x41c8bb4e1e8afdcb8a13c6c1d3b2e07146797024', // Student address from screenshot
    issueDate: new Date('2024-01-25').toISOString(),
    issuerName: ISSUER_NAME,
    certificateIpfsHash: PUBLIC_VIEWABLE_IPFS_CID,
    metadataIpfsUrl: 'ipfs://QmMetadataHashStudent/metadata.json',
    qrCodeUrl: generateQrCodeUrl('txhash005_student_test'),
  },
].map(ensurePublicDocumentCID); // Ensure defaults also use the public CID

// Initialize with default certificates for SSR consistency
let MOCK_CERTIFICATES: Certificate[] = defaultMockCertificates.map(ensurePublicDocumentCID);

// Function to initialize certificates from localStorage (client-side only)
const initializeCertificatesFromStorage = () => {
  if (typeof window !== 'undefined' && window.localStorage) {
    const storedCerts = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (storedCerts) {
      try {
        const parsedCerts = JSON.parse(storedCerts) as Certificate[];
        // Ensure ALL certificates loaded from localStorage use the public viewable CID for the document
        MOCK_CERTIFICATES = parsedCerts.map(ensurePublicDocumentCID);
      } catch (e) {
        console.error("Failed to parse certificates from localStorage, using defaults.", e);
        MOCK_CERTIFICATES = defaultMockCertificates.map(ensurePublicDocumentCID);
        localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(MOCK_CERTIFICATES));
      }
    } else {
      MOCK_CERTIFICATES = defaultMockCertificates.map(ensurePublicDocumentCID);
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(MOCK_CERTIFICATES));
    }
  }
};

export const getMockCertificates = (): Certificate[] => {
    // Initialize from localStorage on first call (client-side only)
    if (typeof window !== 'undefined' && !(window as any).__certificatesInitialized) {
      initializeCertificatesFromStorage();
      (window as any).__certificatesInitialized = true;
    }

    // Always re-process on get, to ensure consistency if MOCK_CERTIFICATES was mutated elsewhere without this.
    // And to handle dynamic aspects like QR code generation if origin changes.
    return MOCK_CERTIFICATES.map(ensurePublicDocumentCID);
};

const saveCertificatesToLocalStorage = () => {
  if (typeof window !== 'undefined' && window.localStorage) {
    // Ensure all certs being saved use the public CID for document and have fresh QR codes
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(MOCK_CERTIFICATES.map(ensurePublicDocumentCID)));
  }
};

export const addCertificateToMockData = (newCert: Certificate) => {
  // Ensure the new certificate uses the public viewable CID for document and has a fresh QR code
  const certToAdd = ensurePublicDocumentCID(newCert);
  
  // Avoid duplicates by ID if re-issuing or if ID generation isn't perfectly unique in mock
  const existingIndex = MOCK_CERTIFICATES.findIndex(cert => cert.id === certToAdd.id);
  if (existingIndex > -1) {
    MOCK_CERTIFICATES[existingIndex] = certToAdd; // Update existing
  } else {
    MOCK_CERTIFICATES.push(certToAdd); // Add new
  }
  saveCertificatesToLocalStorage();
};

// Exporting the array directly is generally fine if mutations are handled through dedicated functions like addCertificateToMockData
// However, getMockCertificates() is safer to ensure processed data.
export { MOCK_CERTIFICATES };
