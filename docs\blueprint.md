# **App Name**: CertiProof

## Core Features:

- Wallet Connect: Wallet connection using MetaMask for admins, students, and verifiers.
- Admin Dashboard: Admin dashboard to upload certificates, enter student names, and mint certificates.
- Certificate Minting: Minting certificates as NFTs and/or as hash-based entries (requiring less gas).
- IPFS Storage: Store the certificate files or metadata URIs securely on IPFS using Web3.storage.
- Student Certificate View: Student profile page where students can view certificates issued to their wallet address.
- Certificate Verification: Verification interface where anyone can verify a certificate’s authenticity by providing either a wallet address or a certificate hash. The tool will use an LLM to incorporate validation data to make its decision to sign off or not.
- QR Code Generation: Generate QR codes for each issued certificate that link to the verification page.

## Style Guidelines:

- Primary color: A calm blue (#64B5F6), evoking trust and security.
- Background color: A light grey (#F0F4F8), for clean and professional feel.
- Accent color: A muted teal (#4DB6AC), complementing the primary color while drawing the user's attention.
- Body and headline font: 'Inter' sans-serif font for a modern, neutral look.
- Minimalist icons from a set like <PERSON>ather or Phosphor, in the primary color.
- Clean and modular layout using Tailwind CSS grid system for responsiveness.
- Subtle transitions and loading animations using simple CSS transitions or the React Transition Group add-on library.