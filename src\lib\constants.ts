
// All caps for constants is a common convention
export const POLYG<PERSON>_MUMBAI_CHAIN_ID = 80001;
export const POLYGON_MUMBAI_CHAIN_ID_HEX = '0x13881'; // Hex for 80001

// For demonstration purposes, add admin addresses here (lowercase)
// In a real app, this would be managed by the smart contract or a secure off-chain list
export const ADMIN_ADDRESSES: string[] = [
  '0x690baed10f9841c101355837ffd4bc4725a59e52'.toLowerCase(),
  // Add your admin test addresses here in lowercase
];

export const APP_NAME = "CertiProof";
export const ISSUER_NAME = "University of Innovation"; // Default issuer name

// Placeholder for Smart Contract ABI and Address
// Replace with your actual ABI and deployed contract address
export const CERTIFICATE_CONTRACT_ABI: any[] = [
  // Example ABI structure - replace with actual
  // {
  //   "inputs": [
  //     { "internalType": "address", "name": "studentAddress", "type": "address" },
  //     { "internalType": "string", "name": "metadataIpfsUrl", "type": "string" }
  //   ],
  //   "name": "issueCertificate",
  //   "outputs": [],
  //   "stateMutability": "nonpayable",
  //   "type": "function"
  // },
  // {
  //   "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }],
  //   "name": "getCertificatesByOwner",
  //   "outputs": [{ "internalType": "string[]", "name": "", "type": "string[]" }],
  //   "stateMutability": "view",
  //   "type": "function"
  // },
  // {
  //   "inputs": [{ "internalType": "string", "name": "certificateId", "type": "string" }],
  //   "name": "getCertificateById",
  //   "outputs": [{ "internalType": "string", "name": "metadataIpfsUrl", "type": "string" }], // Example
  //   "stateMutability": "view",
  //   "type": "function"
  // }
];
export const CERTIFICATE_CONTRACT_ADDRESS = '0xYOUR_CONTRACT_ADDRESS_HERE'; // Replace with your deployed contract address

export const IPFS_GATEWAY_PREFIX = 'https://ipfs.io/ipfs/'; // Or your preferred public/private gateway
// A known public CID for a viewable file (IPFS Logo SVG)
export const PUBLIC_VIEWABLE_IPFS_CID = 'QmWt2dzQ2szyN66XBAW7TBsxN9FGrEehJ9sJv5sXm9w71x';

// QR Code API
export const QR_CODE_API_URL = 'https://api.qrserver.com/v1/create-qr-code/';

